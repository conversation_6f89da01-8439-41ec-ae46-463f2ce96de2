package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceUnitAward;
import com.hl.archive.domain.entity.PoliceUnitAwardToVWjBzjlDwryMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__399;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__399.class,
    uses = {ConversionUtils.class,PoliceUnitAwardToVWjBzjlDwryMapper.class},
    imports = {}
)
public interface VWjBzjlDwryToPoliceUnitAwardMapper extends BaseMapper<VWjBzjlDwry, PoliceUnitAward> {
  @Mapping(
      target = "supervisorName",
      source = "xm"
  )
  @Mapping(
      target = "unit",
      source = "dwmc"
  )
  @Mapping(
      target = "documentNumber",
      source = "bzwh"
  )
  @Mapping(
      target = "supervisorCode",
      source = "jh"
  )
  @Mapping(
      target = "awardOrgan",
      source = "jljgmc"
  )
  @Mapping(
      target = "awardName",
      source = "jlmc"
  )
  @Mapping(
      target = "awardTime",
      source = "bzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceUnitAward convert(VWjBzjlDwry source);

  @Mapping(
      target = "supervisorName",
      source = "xm"
  )
  @Mapping(
      target = "unit",
      source = "dwmc"
  )
  @Mapping(
      target = "documentNumber",
      source = "bzwh"
  )
  @Mapping(
      target = "supervisorCode",
      source = "jh"
  )
  @Mapping(
      target = "awardOrgan",
      source = "jljgmc"
  )
  @Mapping(
      target = "awardName",
      source = "jlmc"
  )
  @Mapping(
      target = "awardTime",
      source = "bzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceUnitAward convert(VWjBzjlDwry source, @MappingTarget PoliceUnitAward target);
}
