package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMarriageStatus;
import com.hl.archive.domain.entity.PoliceMarriageStatusToVWjBrHyzkMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__399;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__399.class,
    uses = {ConversionUtils.class,PoliceMarriageStatusToVWjBrHyzkMapper.class},
    imports = {}
)
public interface VWjBrHyzkToPoliceMarriageStatusMapper extends BaseMapper<VWjBrHyzk, PoliceMarriageStatus> {
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "changeDate",
      source = "bhsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "changeStatus",
      source = "bhhyztmc"
  )
  @Mapping(
      target = "marriageStatus",
      source = "hyztmc"
  )
  PoliceMarriageStatus convert(VWjBrHyzk source);

  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "changeDate",
      source = "bhsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "name",
      source = "xm"
  )
  @Mapping(
      target = "changeStatus",
      source = "bhhyztmc"
  )
  @Mapping(
      target = "marriageStatus",
      source = "hyztmc"
  )
  PoliceMarriageStatus convert(VWjBrHyzk source, @MappingTarget PoliceMarriageStatus target);
}
