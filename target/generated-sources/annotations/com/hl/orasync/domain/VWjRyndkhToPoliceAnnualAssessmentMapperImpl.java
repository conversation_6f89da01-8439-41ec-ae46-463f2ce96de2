package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceAnnualAssessment;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-27T17:33:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyndkhToPoliceAnnualAssessmentMapperImpl implements VWjRyndkhToPoliceAnnualAssessmentMapper {

    @Override
    public PoliceAnnualAssessment convert(VWjRyndkh source) {
        if ( source == null ) {
            return null;
        }

        PoliceAnnualAssessment policeAnnualAssessment = new PoliceAnnualAssessment();

        policeAnnualAssessment.setAssessmentResult( source.getKhjg() );
        policeAnnualAssessment.setIdCard( source.getGmsfhm() );
        policeAnnualAssessment.setAssessmentCategory( source.getKclb() );
        policeAnnualAssessment.setAssessmentYear( source.getKcnd() );

        return policeAnnualAssessment;
    }

    @Override
    public PoliceAnnualAssessment convert(VWjRyndkh source, PoliceAnnualAssessment target) {
        if ( source == null ) {
            return target;
        }

        target.setAssessmentResult( source.getKhjg() );
        target.setIdCard( source.getGmsfhm() );
        target.setAssessmentCategory( source.getKclb() );
        target.setAssessmentYear( source.getKcnd() );

        return target;
    }
}
