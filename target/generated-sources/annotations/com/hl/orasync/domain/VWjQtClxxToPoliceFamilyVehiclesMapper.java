package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyVehicles;
import com.hl.archive.domain.entity.PoliceFamilyVehiclesToVWjQtClxxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__399;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__399.class,
    uses = {ConversionUtils.class,PoliceFamilyVehiclesToVWjQtClxxMapper.class},
    imports = {}
)
public interface VWjQtClxxToPoliceFamilyVehiclesMapper extends BaseMapper<VWjQtClxx, PoliceFamilyVehicles> {
  @Mapping(
      target = "saleAmount",
      source = "csjg",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "vehicleBrand",
      source = "clpp"
  )
  @Mapping(
      target = "licensePlate",
      source = "hphm"
  )
  @Mapping(
      target = "vehicleSource",
      source = "cllymc"
  )
  @Mapping(
      target = "ownerName",
      source = "xmCqr"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "transactionAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "saleDate",
      source = "cssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "transactionDate",
      source = "gmsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "vehicleDisposition",
      source = "clqxmc"
  )
  PoliceFamilyVehicles convert(VWjQtClxx source);

  @Mapping(
      target = "saleAmount",
      source = "csjg",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "vehicleBrand",
      source = "clpp"
  )
  @Mapping(
      target = "licensePlate",
      source = "hphm"
  )
  @Mapping(
      target = "vehicleSource",
      source = "cllymc"
  )
  @Mapping(
      target = "ownerName",
      source = "xmCqr"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "transactionAmount",
      source = "je",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "saleDate",
      source = "cssj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "transactionDate",
      source = "gmsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "vehicleDisposition",
      source = "clqxmc"
  )
  PoliceFamilyVehicles convert(VWjQtClxx source, @MappingTarget PoliceFamilyVehicles target);
}
