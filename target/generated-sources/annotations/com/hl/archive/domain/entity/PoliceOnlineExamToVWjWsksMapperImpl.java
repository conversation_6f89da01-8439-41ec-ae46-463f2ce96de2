package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWsks;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-27T17:33:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceOnlineExamToVWjWsksMapperImpl implements PoliceOnlineExamToVWjWsksMapper {

    @Override
    public VWjWsks convert(PoliceOnlineExam source) {
        if ( source == null ) {
            return null;
        }

        VWjWsks vWjWsks = new VWjWsks();

        if ( source.getEndTime() != null ) {
            vWjWsks.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndTime() ) );
        }
        vWjWsks.setDf( source.getScore() );
        if ( source.getQuestionCount() != null ) {
            vWjWsks.setTmgs( BigDecimal.valueOf( source.getQuestionCount() ) );
        }
        vWjWsks.setSfjj( source.getSubmitStatus() );
        vWjWsks.setGmsfhm( source.getIdCard() );
        vWjWsks.setSjmc( source.getExamPaperName() );
        if ( source.getExamDuration() != null ) {
            vWjWsks.setKssc( BigDecimal.valueOf( source.getExamDuration() ) );
        }
        if ( source.getStartTime() != null ) {
            vWjWsks.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartTime() ) );
        }

        return vWjWsks;
    }

    @Override
    public VWjWsks convert(PoliceOnlineExam source, VWjWsks target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getEndTime() != null ) {
            target.setJssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEndTime() ) );
        }
        else {
            target.setJssj( null );
        }
        target.setDf( source.getScore() );
        if ( source.getQuestionCount() != null ) {
            target.setTmgs( BigDecimal.valueOf( source.getQuestionCount() ) );
        }
        else {
            target.setTmgs( null );
        }
        target.setSfjj( source.getSubmitStatus() );
        target.setGmsfhm( source.getIdCard() );
        target.setSjmc( source.getExamPaperName() );
        if ( source.getExamDuration() != null ) {
            target.setKssc( BigDecimal.valueOf( source.getExamDuration() ) );
        }
        else {
            target.setKssc( null );
        }
        if ( source.getStartTime() != null ) {
            target.setKssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getStartTime() ) );
        }
        else {
            target.setKssj( null );
        }

        return target;
    }
}
