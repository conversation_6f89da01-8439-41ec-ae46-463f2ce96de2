package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrYscg;
import com.hl.orasync.domain.VWjBrYscgToPoliceOverseasTravelMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__399;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__399.class,
    uses = {ConversionUtils.class,VWjBrYscgToPoliceOverseasTravelMapper.class},
    imports = {}
)
public interface PoliceOverseasTravelToVWjBrYscgMapper extends BaseMapper<PoliceOverseasTravel, VWjBrYscg> {
  @Mapping(
      target = "sdgj",
      source = "destinationCountry"
  )
  @Mapping(
      target = "jssj",
      source = "endDate"
  )
  @Mapping(
      target = "sy",
      source = "travelReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hzhm",
      source = "passportNumber"
  )
  @Mapping(
      target = "spjgmc",
      source = "approvalAuthority"
  )
  @Mapping(
      target = "kssj",
      source = "startDate"
  )
  VWjBrYscg convert(PoliceOverseasTravel source);

  @Mapping(
      target = "sdgj",
      source = "destinationCountry"
  )
  @Mapping(
      target = "jssj",
      source = "endDate"
  )
  @Mapping(
      target = "sy",
      source = "travelReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hzhm",
      source = "passportNumber"
  )
  @Mapping(
      target = "spjgmc",
      source = "approvalAuthority"
  )
  @Mapping(
      target = "kssj",
      source = "startDate"
  )
  VWjBrYscg convert(PoliceOverseasTravel source, @MappingTarget VWjBrYscg target);
}
