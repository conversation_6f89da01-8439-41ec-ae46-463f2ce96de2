package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrWjdc;
import com.hl.orasync.domain.VWjBrWjdcToPoliceOrganizationalInquiryMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__399;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__399.class,
    uses = {ConversionUtils.class,VWjBrWjdcToPoliceOrganizationalInquiryMapper.class},
    imports = {}
)
public interface PoliceOrganizationalInquiryToVWjBrWjdcMapper extends BaseMapper<PoliceOrganizationalInquiry, VWjBrWjdc> {
  @Mapping(
      target = "dcrq",
      source = "handlingDate"
  )
  @Mapping(
      target = "dcjg",
      source = "handlingAuthority"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "wfqk",
      source = "suspectedViolations"
  )
  VWjBrWjdc convert(PoliceOrganizationalInquiry source);

  @Mapping(
      target = "dcrq",
      source = "handlingDate"
  )
  @Mapping(
      target = "dcjg",
      source = "handlingAuthority"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "wfqk",
      source = "suspectedViolations"
  )
  VWjBrWjdc convert(PoliceOrganizationalInquiry source, @MappingTarget VWjBrWjdc target);
}
