package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjtcy;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-27T17:33:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyMembersToVWjRyjtcyMapperImpl implements PoliceFamilyMembersToVWjRyjtcyMapper {

    @Override
    public VWjRyjtcy convert(PoliceFamilyMembers source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjtcy vWjRyjtcy = new VWjRyjtcy();

        vWjRyjtcy.setCsrq( source.getBirthDate() );
        vWjRyjtcy.setXm( source.getMemberName() );
        vWjRyjtcy.setGzdw( source.getWorkUnitPosition() );
        vWjRyjtcy.setGmsfhm( source.getIdCard() );
        vWjRyjtcy.setZzmm( source.getPoliticalStatus() );
        vWjRyjtcy.setSjhm( source.getMobilePhone() );
        vWjRyjtcy.setRygx( source.getRelationship() );

        return vWjRyjtcy;
    }

    @Override
    public VWjRyjtcy convert(PoliceFamilyMembers source, VWjRyjtcy target) {
        if ( source == null ) {
            return target;
        }

        target.setCsrq( source.getBirthDate() );
        target.setXm( source.getMemberName() );
        target.setGzdw( source.getWorkUnitPosition() );
        target.setGmsfhm( source.getIdCard() );
        target.setZzmm( source.getPoliticalStatus() );
        target.setSjhm( source.getMobilePhone() );
        target.setRygx( source.getRelationship() );

        return target;
    }
}
